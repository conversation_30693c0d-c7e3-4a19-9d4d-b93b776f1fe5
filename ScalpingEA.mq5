//+------------------------------------------------------------------+
//|                                                   ScalpingEA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade\Trade.mqh>

//--- Input parameters
input int      EMA_Period = 20;           // EMA Period
input double   LotSize = 0.01;            // Lot Size
input int      SL_Buffer = 5;             // Stop Loss Buffer (pips)
input int      TrailingStep = 5;          // Trailing Step (pips)
input int      MinProfitToTrail = 5;      // Minimum Profit to Start Trailing (pips)
input int      StartHour = 7;             // Trading Start Hour
input int      EndHour = 17;              // Trading End Hour
input int      MagicNumber = 123456;      // Magic Number
input bool     AllowOnlyOnePosition = true; // Allow Only One Position at a Time

//--- Global variables
CTrade trade;
int ema_handle;
double ema_buffer[];
datetime last_bar_time = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize EMA indicator
    ema_handle = iMA(_Symbol, PERIOD_CURRENT, EMA_Period, 0, MODE_EMA, PRICE_CLOSE);
    if(ema_handle == INVALID_HANDLE)
    {
        Print("Failed to create EMA indicator");
        return INIT_FAILED;
    }
    
    // Set trade parameters
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(10);
    
    // Set array as series
    ArraySetAsSeries(ema_buffer, true);
    
    Print("Scalping EA initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(ema_handle != INVALID_HANDLE)
        IndicatorRelease(ema_handle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if new bar formed
    if(!IsNewBar())
        return;
    
    // Check trading time
    if(!IsTradingTime())
        return;
    
    // Update EMA values
    if(CopyBuffer(ema_handle, 0, 0, 3, ema_buffer) < 3)
        return;
    
    // Check for existing positions
    if(PositionsTotal() > 0)
    {
        ManagePositions();
        if(AllowOnlyOnePosition)
            return;
    }
    
    // Look for trading signals
    CheckBuySignal();
    CheckSellSignal();
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                         |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime current_time = iTime(_Symbol, PERIOD_CURRENT, 0);
    if(current_time != last_bar_time)
    {
        last_bar_time = current_time;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if within trading hours                                   |
//+------------------------------------------------------------------+
bool IsTradingTime()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    return (dt.hour >= StartHour && dt.hour < EndHour);
}

//+------------------------------------------------------------------+
//| Check for buy signal                                            |
//+------------------------------------------------------------------+
void CheckBuySignal()
{
    // Get candle data
    double open1 = iOpen(_Symbol, PERIOD_CURRENT, 1);
    double close1 = iClose(_Symbol, PERIOD_CURRENT, 1);
    double high1 = iHigh(_Symbol, PERIOD_CURRENT, 1);
    double low1 = iLow(_Symbol, PERIOD_CURRENT, 1);
    
    double open2 = iOpen(_Symbol, PERIOD_CURRENT, 2);
    double close2 = iClose(_Symbol, PERIOD_CURRENT, 2);
    double high2 = iHigh(_Symbol, PERIOD_CURRENT, 2);
    double low2 = iLow(_Symbol, PERIOD_CURRENT, 2);
    
    // Check for 2 consecutive bullish candles
    bool bullish1 = close1 > open1;
    bool bullish2 = close2 > open2;
    
    if(!bullish1 || !bullish2)
        return;
    
    // Check if second candle is larger than first (stronger momentum)
    double candle1_body = MathAbs(close1 - open1);
    double candle2_body = MathAbs(close2 - open2);

    if(candle1_body <= candle2_body)
        return;
    
    // Check if price is above EMA20
    if(close1 <= ema_buffer[1])
        return;
    
    // Calculate stop loss
    double sl = low1 - SL_Buffer * _Point;
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    
    // Open buy position
    if(trade.Buy(LotSize, _Symbol, current_price, sl, 0, "Scalping Buy"))
    {
        Print("Buy order opened at ", current_price, " SL: ", sl);
    }
}

//+------------------------------------------------------------------+
//| Check for sell signal                                           |
//+------------------------------------------------------------------+
void CheckSellSignal()
{
    // Get candle data
    double open1 = iOpen(_Symbol, PERIOD_CURRENT, 1);
    double close1 = iClose(_Symbol, PERIOD_CURRENT, 1);
    double high1 = iHigh(_Symbol, PERIOD_CURRENT, 1);
    double low1 = iLow(_Symbol, PERIOD_CURRENT, 1);
    
    double open2 = iOpen(_Symbol, PERIOD_CURRENT, 2);
    double close2 = iClose(_Symbol, PERIOD_CURRENT, 2);
    double high2 = iHigh(_Symbol, PERIOD_CURRENT, 2);
    double low2 = iLow(_Symbol, PERIOD_CURRENT, 2);
    
    // Check for 2 consecutive bearish candles
    bool bearish1 = close1 < open1;
    bool bearish2 = close2 < open2;
    
    if(!bearish1 || !bearish2)
        return;
    
    // Check if second candle is larger than first (stronger momentum)
    double candle1_body = MathAbs(close1 - open1);
    double candle2_body = MathAbs(close2 - open2);

    if(candle1_body <= candle2_body)
        return;
    
    // Check if price is below EMA20
    if(close1 >= ema_buffer[1])
        return;
    
    // Calculate stop loss
    double sl = high1 + SL_Buffer * _Point;
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    // Open sell position
    if(trade.Sell(LotSize, _Symbol, current_price, sl, 0, "Scalping Sell"))
    {
        Print("Sell order opened at ", current_price, " SL: ", sl);
    }
}

//+------------------------------------------------------------------+
//| Manage existing positions                                       |
//+------------------------------------------------------------------+
void ManagePositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByIndex(i))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
               PositionGetInteger(POSITION_MAGIC) == MagicNumber)
            {
                TrailStopLoss();
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Trail stop loss function                                        |
//+------------------------------------------------------------------+
void TrailStopLoss()
{
    ulong ticket = PositionGetInteger(POSITION_TICKET);
    double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    double current_sl = PositionGetDouble(POSITION_SL);
    ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

    double current_price = (pos_type == POSITION_TYPE_BUY) ?
                          SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    // Calculate profit in pips
    double profit_pips = 0;
    if(pos_type == POSITION_TYPE_BUY)
        profit_pips = (current_price - open_price) / _Point;
    else
        profit_pips = (open_price - current_price) / _Point;

    // Only trail if position is in at least minimum profit
    if(profit_pips < MinProfitToTrail)
        return;

    // Get previous candle data for trailing
    double prev_high = iHigh(_Symbol, PERIOD_CURRENT, 1);
    double prev_low = iLow(_Symbol, PERIOD_CURRENT, 1);

    double new_sl = 0;

    if(pos_type == POSITION_TYPE_BUY)
    {
        // Trail SL to low of previous candle
        new_sl = prev_low;

        // Only move SL up, never down
        if(new_sl > current_sl && new_sl < current_price)
        {
            if(trade.PositionModify(ticket, new_sl, 0))
            {
                Print("Buy position SL trailed to: ", new_sl);
            }
        }
    }
    else if(pos_type == POSITION_TYPE_SELL)
    {
        // Trail SL to high of previous candle
        new_sl = prev_high;

        // Only move SL down, never up
        if((current_sl == 0 || new_sl < current_sl) && new_sl > current_price)
        {
            if(trade.PositionModify(ticket, new_sl, 0))
            {
                Print("Sell position SL trailed to: ", new_sl);
            }
        }
    }
}
 