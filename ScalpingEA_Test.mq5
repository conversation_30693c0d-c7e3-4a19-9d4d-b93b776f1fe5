//+------------------------------------------------------------------+
//|                                            ScalpingEA_Test.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property script_show_inputs

//--- Input parameters
input int TestBars = 100;        // Number of bars to test
input bool ShowDetails = true;   // Show detailed output

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== Scalping EA Test Script ===");
    
    // Test EMA calculation
    TestEMACalculation();
    
    // Test candle pattern detection
    TestCandlePatterns();
    
    // Test time filter
    TestTimeFilter();
    
    Print("=== Test Complete ===");
}

//+------------------------------------------------------------------+
//| Test EMA calculation                                             |
//+------------------------------------------------------------------+
void TestEMACalculation()
{
    Print("Testing EMA calculation...");
    
    int ema_handle = iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_CLOSE);
    if(ema_handle == INVALID_HANDLE)
    {
        Print("ERROR: Failed to create EMA indicator");
        return;
    }
    
    double ema_buffer[];
    ArraySetAsSeries(ema_buffer, true);
    
    if(CopyBuffer(ema_handle, 0, 0, 5, ema_buffer) < 5)
    {
        Print("ERROR: Failed to copy EMA buffer");
        IndicatorRelease(ema_handle);
        return;
    }
    
    Print("EMA values (last 5 bars):");
    for(int i = 0; i < 5; i++)
    {
        Print("Bar ", i, ": EMA = ", DoubleToString(ema_buffer[i], 5));
    }
    
    IndicatorRelease(ema_handle);
    Print("EMA test completed successfully");
}

//+------------------------------------------------------------------+
//| Test candle pattern detection                                    |
//+------------------------------------------------------------------+
void TestCandlePatterns()
{
    Print("Testing candle pattern detection...");
    
    int bullish_count = 0;
    int bearish_count = 0;
    int momentum_signals = 0;
    
    for(int i = 2; i < TestBars; i++)
    {
        // Get candle data
        double open1 = iOpen(_Symbol, PERIOD_CURRENT, i-1);
        double close1 = iClose(_Symbol, PERIOD_CURRENT, i-1);
        double open2 = iOpen(_Symbol, PERIOD_CURRENT, i);
        double close2 = iClose(_Symbol, PERIOD_CURRENT, i);
        
        // Check for consecutive bullish candles
        bool bullish1 = close1 > open1;
        bool bullish2 = close2 > open2;
        
        if(bullish1 && bullish2)
        {
            bullish_count++;
            
            // Check momentum
            double candle1_body = MathAbs(close1 - open1);
            double candle2_body = MathAbs(close2 - open2);
            
            if(candle1_body > candle2_body)
            {
                momentum_signals++;
                if(ShowDetails)
                    Print("Bullish momentum signal at bar ", i-1);
            }
        }
        
        // Check for consecutive bearish candles
        bool bearish1 = close1 < open1;
        bool bearish2 = close2 < open2;
        
        if(bearish1 && bearish2)
        {
            bearish_count++;
            
            // Check momentum
            double candle1_body = MathAbs(close1 - open1);
            double candle2_body = MathAbs(close2 - open2);
            
            if(candle1_body > candle2_body)
            {
                momentum_signals++;
                if(ShowDetails)
                    Print("Bearish momentum signal at bar ", i-1);
            }
        }
    }
    
    Print("Pattern analysis results:");
    Print("Consecutive bullish patterns: ", bullish_count);
    Print("Consecutive bearish patterns: ", bearish_count);
    Print("Momentum signals detected: ", momentum_signals);
}

//+------------------------------------------------------------------+
//| Test time filter                                                 |
//+------------------------------------------------------------------+
void TestTimeFilter()
{
    Print("Testing time filter...");
    
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    Print("Current broker time: ", dt.hour, ":", dt.min);
    
    bool trading_allowed = (dt.hour >= 7 && dt.hour < 17);
    Print("Trading allowed now: ", trading_allowed ? "YES" : "NO");
    
    // Test different hours
    Print("Trading hours analysis:");
    for(int hour = 0; hour < 24; hour++)
    {
        bool allowed = (hour >= 7 && hour < 17);
        if(ShowDetails)
            Print("Hour ", hour, ": ", allowed ? "ALLOWED" : "BLOCKED");
    }
}
