//+------------------------------------------------------------------+
//|                                          ScalpingEA_Optimized.mq5 |
//|                                                    Best Scalper |
//+------------------------------------------------------------------+
#property version   "2.00"
#include <Trade\Trade.mqh>

// Inputs
input double   Lots = 0.01;
input int      EMA_Period = 20;
input int      SL_Pips = 5;
input int      Trail_Start = 5;
input int      Start_Hour = 7;
input int      End_Hour = 17;
input int      Magic = 12345;

// Globals
CTrade trade;
int ema;
datetime last_bar;

int OnInit() {
    ema = iMA(_Symbol, 0, EMA_Period, 0, MODE_EMA, PRICE_CLOSE);
    if(ema == INVALID_HANDLE) return INIT_FAILED;
    trade.SetExpertMagicNumber(Magic);
    return INIT_SUCCEEDED;
}

void OnDeinit(const int reason) {
    IndicatorRelease(ema);
}

void OnTick() {
    if(!NewBar() || !TradingTime() || PositionsTotal() > 0) {
        if(PositionsTotal() > 0) TrailStop();
        return;
    }

    double ema_val[];
    if(CopyBuffer(ema, 0, 1, 1, ema_val) < 1) return;

    // Get candle data
    double o1 = iOpen(_Symbol, 0, 1), c1 = iClose(_Symbol, 0, 1);
    double o2 = iOpen(_Symbol, 0, 2), c2 = iClose(_Symbol, 0, 2);
    double h1 = iHigh(_Symbol, 0, 1), l1 = iLow(_Symbol, 0, 1);

    // Check signals
    bool bull1 = c1 > o1, bull2 = c2 > o2;
    bool bear1 = c1 < o1, bear2 = c2 < o2;
    bool momentum = MathAbs(c1-o1) > MathAbs(c2-o2);

    // BUY Signal
    if(bull1 && bull2 && momentum && c1 > ema_val[0]) {
        double sl = l1 - SL_Pips * _Point;
        trade.Buy(Lots, _Symbol, 0, sl, 0);
    }

    // SELL Signal
    if(bear1 && bear2 && momentum && c1 < ema_val[0]) {
        double sl = h1 + SL_Pips * _Point;
        trade.Sell(Lots, _Symbol, 0, sl, 0);
    }
}

bool NewBar() {
    datetime current = iTime(_Symbol, 0, 0);
    if(current != last_bar) {
        last_bar = current;
        return true;
    }
    return false;
}

bool TradingTime() {
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    return (dt.hour >= Start_Hour && dt.hour < End_Hour);
}

void TrailStop() {
    for(int i = 0; i < PositionsTotal(); i++) {
        if(!PositionSelectByIndex(i) || PositionGetString(POSITION_SYMBOL) != _Symbol) continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
        double current_sl = PositionGetDouble(POSITION_SL);
        ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

        double current_price = (type == POSITION_TYPE_BUY) ?
                              SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                              SymbolInfoDouble(_Symbol, SYMBOL_ASK);

        double profit_pips = (type == POSITION_TYPE_BUY) ?
                            (current_price - open_price) / _Point :
                            (open_price - current_price) / _Point;

        if(profit_pips < Trail_Start) continue;

        double new_sl = 0;
        if(type == POSITION_TYPE_BUY) {
            new_sl = iLow(_Symbol, 0, 1);
            if(new_sl > current_sl && new_sl < current_price)
                trade.PositionModify(ticket, new_sl, 0);
        } else {
            new_sl = iHigh(_Symbol, 0, 1);
            if((current_sl == 0 || new_sl < current_sl) && new_sl > current_price)
                trade.PositionModify(ticket, new_sl, 0);
        }
    }
}

//+------------------------------------------------------------------+
//| Manage existing positions                                       |
//+------------------------------------------------------------------+
void ManagePositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByIndex(i))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
               PositionGetInteger(POSITION_MAGIC) == MagicNumber)
            {
                TrailStopLoss();
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Trail stop loss function                                        |
//+------------------------------------------------------------------+
void TrailStopLoss()
{
    ulong ticket = PositionGetInteger(POSITION_TICKET);
    double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    double current_sl = PositionGetDouble(POSITION_SL);
    ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

    double current_price = (pos_type == POSITION_TYPE_BUY) ?
                          SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    // Calculate profit in pips
    double profit_pips = 0;
    if(pos_type == POSITION_TYPE_BUY)
        profit_pips = (current_price - open_price) / _Point;
    else
        profit_pips = (open_price - current_price) / _Point;

    // Only trail if position is in at least minimum profit
    if(profit_pips < MinProfitToTrail)
        return;

    // Get previous candle data for trailing
    double prev_high = iHigh(_Symbol, PERIOD_CURRENT, 1);
    double prev_low = iLow(_Symbol, PERIOD_CURRENT, 1);

    double new_sl = 0;

    if(pos_type == POSITION_TYPE_BUY)
    {
        // Trail SL to low of previous candle
        new_sl = prev_low;

        // Only move SL up, never down
        if(new_sl > current_sl && new_sl < current_price)
        {
            if(trade.PositionModify(ticket, new_sl, 0))
            {
                Print("Buy position SL trailed to: ", new_sl);
            }
        }
    }
    else if(pos_type == POSITION_TYPE_SELL)
    {
        // Trail SL to high of previous candle
        new_sl = prev_high;

        // Only move SL down, never up
        if((current_sl == 0 || new_sl < current_sl) && new_sl > current_price)
        {
            if(trade.PositionModify(ticket, new_sl, 0))
            {
                Print("Sell position SL trailed to: ", new_sl);
            }
        }
    }
}
 