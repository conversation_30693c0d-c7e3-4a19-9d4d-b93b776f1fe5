# Scalping EA for MT5

A professional scalping Expert Advisor designed for M5 and M15 timeframes that uses momentum-based entry signals with EMA confirmation and dynamic trailing stop loss.

## Features

### Entry Conditions

**BUY Signal:**
- 2 or more consecutive bullish candles (close > open)
- Second candle body is larger than the first (stronger momentum)
- Current price is above EMA20
- Entry at market price when signal is confirmed
- Initial stop loss below the low of the last bullish candle

**SELL Signal:**
- 2 or more consecutive bearish candles (close < open)
- Second candle body is larger than the first (stronger momentum)
- Current price is below EMA20
- Entry at market price when signal is confirmed
- Initial stop loss above the high of the last bearish candle

### Risk Management

**Trailing Stop Loss:**
- Activates only when position is in at least 5 pips profit (configurable)
- For BUY positions: Trails to the low of the previous candle
- For SELL positions: Trails to the high of the previous candle
- Stop loss only moves in favorable direction (never against the position)

**Time Filter:**
- Only trades during 07:00 to 17:00 broker time (configurable)
- Prevents trading during low liquidity periods

## Input Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| EMA_Period | 20 | Period for the Exponential Moving Average |
| LotSize | 0.01 | Position size in lots |
| SL_Buffer | 5 | Additional buffer for stop loss in pips |
| TrailingStep | 5 | Minimum step for trailing stop in pips |
| MinProfitToTrail | 5 | Minimum profit in pips before trailing starts |
| StartHour | 7 | Trading start hour (broker time) |
| EndHour | 17 | Trading end hour (broker time) |
| MagicNumber | 123456 | Unique identifier for EA trades |
| AllowOnlyOnePosition | true | Limit to one position at a time |

## Installation

1. Copy `ScalpingEA.mq5` to your MT5 `MQL5/Experts` folder
2. Compile the EA in MetaEditor (F7)
3. Attach to M5 or M15 chart
4. Configure input parameters as needed
5. Enable automated trading

## Recommended Settings

### For M5 Timeframe:
- EMA_Period: 20
- LotSize: 0.01 (adjust based on account size)
- SL_Buffer: 3-5 pips
- MinProfitToTrail: 5 pips

### For M15 Timeframe:
- EMA_Period: 20
- LotSize: 0.01 (adjust based on account size)
- SL_Buffer: 5-8 pips
- MinProfitToTrail: 8 pips

## Best Practices

1. **Backtesting:** Always backtest on historical data before live trading
2. **Demo Trading:** Test on demo account first
3. **Risk Management:** Never risk more than 1-2% per trade
4. **Market Conditions:** Works best in trending markets
5. **Spread Consideration:** Use with brokers offering tight spreads
6. **VPS Recommended:** For consistent execution

## Risk Warning

Trading forex involves substantial risk of loss and is not suitable for all investors. Past performance is not indicative of future results. Always trade with money you can afford to lose.

## Support

For questions or issues, please refer to the MQL5 community or your broker's support.

## Version History

- v1.00: Initial release with momentum-based scalping strategy
